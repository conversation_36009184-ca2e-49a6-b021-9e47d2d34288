-- Add project reference to historias table
-- This script adds the id_proyecto field to establish foreign key relationship with proyectos table

-- Add id_proyecto field (positioned after porc_progreso and before estado)
ALTER TABLE historias 
ADD COLUMN id_proyecto INT(11) NULL AFTER porc_progreso;

-- Add foreign key constraint with proper cascading behavior
ALTER TABLE historias 
ADD CONSTRAINT historias_ibfk_1 FOREIGN KEY (id_proyecto) REFERENCES proyectos(id) ON UPDATE CASCADE ON DELETE SET NULL;

-- Create index for better performance
CREATE INDEX idx_id_proyecto ON historias(id_proyecto);

-- Update existing records to ensure they have the default NULL value for id_proyecto
-- (This is optional since the field is already nullable, but included for completeness)
UPDATE historias SET id_proyecto = NULL WHERE id_proyecto IS NULL;
