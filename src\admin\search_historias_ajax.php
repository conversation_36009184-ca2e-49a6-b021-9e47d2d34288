<?php

declare(strict_types=1);

/**
 * AJAX endpoint for searching Historia records by project and query term.
 * Used for autocomplete functionality in task creation and editing forms.
 * 
 * Follows the pattern established by search_modulos_ajax.php for consistency.
 */

// Prevent direct access
if (!defined('__ROOT__')) {
    define('__ROOT__', dirname(dirname(__DIR__)));
}

// Include required files
require_once __ROOT__ . '/config/config.php';
require_once __ROOT__ . '/src/classes/Database.php';
require_once __ROOT__ . '/src/classes/Historia.php';

use App\classes\Database;
use App\classes\Historia;

// Initialize database connection
$database = new Database();
$conexion = $database->getConnection();

// Set JSON response header
header('Content-Type: application/json');

try {
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Método de solicitud no válido.");
    }

    // Get search parameters from POST
    $search_query = trim($_POST['query'] ?? '');
    $id_proyecto = filter_input(INPUT_POST, 'id_proyecto', FILTER_VALIDATE_INT);
    $limit = filter_input(INPUT_POST, 'limit', FILTER_VALIDATE_INT) ?: 10;

    // Validate required parameters
    if (empty($search_query) || strlen($search_query) < 2) {
        echo json_encode([
            'success' => true,
            'results' => [],
            'message' => 'Escriba al menos 2 caracteres para buscar.'
        ]);
        exit;
    }

    if (!$id_proyecto) {
        echo json_encode(['success' => false, 'message' => 'ID de proyecto requerido']);
        exit;
    }

    // Get historias for the project
    $historias = Historia::get_by_proyecto_id($id_proyecto, $conexion);

    // Filter historias by search query
    $results = [];
    $count = 0;
    foreach ($historias as $historia) {
        if ($count >= $limit) {
            break;
        }

        // Check if titulo or descripcion contains the search query (case-insensitive)
        $titulo = $historia->getTitulo() ?? '';
        $descripcion = $historia->getDescripcion() ?? '';
        
        if (stripos($titulo, $search_query) !== false || 
            stripos($descripcion, $search_query) !== false) {
            
            // Create display text with titulo and descripcion if available
            $display_text = $titulo;
            if (!empty($descripcion) && $descripcion !== $titulo) {
                $display_text .= ' - ' . $descripcion;
            }
            
            $results[] = [
                'id' => $historia->getId(),
                'titulo' => $titulo,
                'descripcion' => $descripcion,
                'display_text' => $display_text,
                'porc_progreso' => $historia->getPorcProgreso()
            ];
            $count++;
        }
    }

    echo json_encode(['success' => true, 'results' => $results]);

} catch (Exception $e) {
    error_log("Error in search_historias_ajax.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error al buscar historias: ' . $e->getMessage()]);
}
