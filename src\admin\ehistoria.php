<?php

declare(strict_types=1);

use App\classes\Historia;
use App\classes\Proyecto;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en ehistoria.php.");
	die('Error crítico: No se pudo conectar a la base de datos.');
}

#region region init variables
$historia          = null;
$proyecto_asociado = null;
$titulo            = '';
$descripcion       = '';
$success_text      = '';
$success_display   = 'none';
$error_text        = '';
$error_display     = 'none';
$validation_errors = [];
#endregion init variables

#region region Handle Flash Message Success
if (!empty($_SESSION['flash_message_success'])) {
	$success_text = $_SESSION['flash_message_success'];
	$success_display = 'show';
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region region Handle Flash Message Error
if (!empty($_SESSION['flash_message_error'])) {
	$error_text = $_SESSION['flash_message_error'];
	$error_display = 'show';
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region region Get Historia ID from Session
$historia_id = $_SESSION['historia_edit_id'] ?? null;

if (!$historia_id) {
	$_SESSION['flash_message_error'] = 'No se especificó una historia para editar';
	header('Location: listado-historias');
	exit;
}

try {
	$historia = Historia::get_by_id($historia_id, $conexion);
	
	if (!$historia || !$historia->isActiva()) {
		$_SESSION['flash_message_error'] = 'Historia no encontrada o inactiva';
		header('Location: listado-historias');
		exit;
	}

	// Get associated project
	if ($historia->getIdProyecto()) {
		try {
			$proyecto_asociado = $historia->getProyecto($conexion);
		} catch (Exception $e) {
			// Project not found or error, but continue with historia editing
			$proyecto_asociado = null;
		}
	}

	// Pre-populate form fields if not a POST request
	if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
		$titulo = $historia->getTitulo();
		$descripcion = $historia->getDescripcion();
	}

} catch (Exception $e) {
	$_SESSION['flash_message_error'] = 'Error al cargar la historia: ' . $e->getMessage();
	header('Location: listado-historias');
	exit;
}
#endregion Get Historia ID from Session

#region region Handle AJAX Requests
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
	$action = $_POST['action'];
	$isAjax = isset($_POST['is_ajax']) && $_POST['is_ajax'] == '1';

	if ($isAjax && $action === 'set_project_session') {
		$proyecto_id = filter_input(INPUT_POST, 'proyecto_id', FILTER_VALIDATE_INT);

		if ($proyecto_id) {
			$_SESSION['proyecto_id'] = $proyecto_id;
		}
		exit;
	}
}
#endregion Handle AJAX Requests

#region region Handle Form Submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['action'])) {
	// Get and sanitize form data
	$titulo = trim(filter_input(INPUT_POST, 'titulo', FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?? '');
	$descripcion = trim(filter_input(INPUT_POST, 'descripcion', FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?? '');

	// Validate required fields
	if (empty($titulo)) {
		$validation_errors['titulo'] = 'El título es requerido';
	}

	if (empty($descripcion)) {
		$validation_errors['descripcion'] = 'La descripción es requerida';
	}

	// If no validation errors, proceed with update
	if (empty($validation_errors)) {
		try {
			$historia->setTitulo($titulo);
			$historia->setDescripcion($descripcion);

			$resultado = $historia->actualizar($conexion);

			if ($resultado) {
				// Clear session data
				unset($_SESSION['historia_edit_id']);

				// Set project ID in session for return navigation if project exists
				if ($historia->getIdProyecto()) {
					$_SESSION['proyecto_id'] = $historia->getIdProyecto();
				}

				$_SESSION['flash_message_success'] = 'Historia actualizada exitosamente';
				header('Location: listado-historias');
				exit;
			} else {
				$error_text = 'Error al actualizar la historia. Por favor intente nuevamente.';
				$error_display = 'show';
			}

		} catch (Exception $e) {
			$error_text = 'Error al actualizar la historia: ' . $e->getMessage();
			$error_display = 'show';
		}
	} else {
		$error_text = 'Por favor corrija los errores en el formulario';
		$error_display = 'show';
	}
}
#endregion Handle Form Submission

// Include the view
require_once __ROOT__ . '/views/admin/ehistoria.view.php';
