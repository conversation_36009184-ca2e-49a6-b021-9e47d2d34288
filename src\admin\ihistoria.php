<?php

declare(strict_types=1);

use App\classes\Historia;
use App\classes\Proyecto;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en ihistoria.php.");
	die('Error crítico: No se pudo conectar a la base de datos.');
}

#region region init variables
$titulo                = '';
$descripcion           = '';
$proyecto_seleccionado = null;
$id_proyecto           = null;
$success_text          = '';
$success_display       = 'none';
$error_text            = '';
$error_display         = 'none';
$validation_errors     = [];
#endregion init variables

#region region Handle AJAX Requests FIRST
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
	$action = $_POST['action'];
	$isAjax = isset($_POST['is_ajax']) && $_POST['is_ajax'] == '1';

	if ($isAjax && $action === 'set_project_session') {
		$proyecto_id = filter_input(INPUT_POST, 'proyecto_id', FILTER_VALIDATE_INT);

		if ($proyecto_id) {
			$_SESSION['proyecto_id'] = $proyecto_id;
		}
		exit;
	}
}
#endregion Handle AJAX Requests FIRST

#region region Check Project Session
// Check if project ID is available in session
if (isset($_SESSION['proyecto_id'])) {
	$id_proyecto = (int)$_SESSION['proyecto_id'];
	// DON'T clear the session here - only clear it when we successfully redirect
} else {
	// No project selected, redirect to project selection
	$_SESSION['flash_message_error'] = 'Debe seleccionar un proyecto antes de crear una historia.';
	header('Location: lproyectos');
	exit;
}

try {
	// Get the selected project
	$proyecto_seleccionado = Proyecto::get($id_proyecto, $conexion);
	if (!$proyecto_seleccionado) {
		$_SESSION['flash_message_error'] = 'Proyecto no encontrado.';
		header('Location: lproyectos');
		exit;
	}
} catch (Exception $e) {
	$_SESSION['flash_message_error'] = 'Error al cargar el proyecto: ' . $e->getMessage();
	header('Location: lproyectos');
	exit;
}
#endregion Check Project Session

#region region Handle Flash Message Success
if (!empty($_SESSION['flash_message_success'])) {
	$success_text = $_SESSION['flash_message_success'];
	$success_display = 'show';
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region region Handle Flash Message Error
if (!empty($_SESSION['flash_message_error'])) {
	$error_text = $_SESSION['flash_message_error'];
	$error_display = 'show';
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region region Handle Form Submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['action'])) {
	// Get and sanitize form data
	$titulo = trim(filter_input(INPUT_POST, 'titulo', FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?? '');
	$descripcion = trim(filter_input(INPUT_POST, 'descripcion', FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?? '');

	// Validate required fields
	if (empty($titulo)) {
		$validation_errors['titulo'] = 'El título es requerido';
	}

	if (empty($descripcion)) {
		$validation_errors['descripcion'] = 'La descripción es requerida';
	}

	// If no validation errors, proceed with creation
	if (empty($validation_errors)) {
		try {
			$historia = new Historia();
			$historia->setTitulo($titulo);
			$historia->setDescripcion($descripcion);
			$historia->setPorcProgreso(0.0); // Default progress
			$historia->setIdProyecto($id_proyecto); // Assign to project
			$historia->setEstado(1); // Active state

			$historia_id = $historia->crear($conexion);

			if ($historia_id) {
				$_SESSION['flash_message_success'] = 'Historia creada exitosamente';
				// Keep project ID in session for lhistorias.view.php
				$_SESSION['proyecto_id'] = $id_proyecto;
				header('Location: listado-historias');
				exit;
			} else {
				$error_text = 'Error al crear la historia. Por favor intente nuevamente.';
				$error_display = 'show';
			}

		} catch (Exception $e) {
			$error_text = 'Error al crear la historia: ' . $e->getMessage();
			$error_display = 'show';
		}
	} else {
		$error_text = 'Por favor corrija los errores en el formulario';
		$error_display = 'show';
	}
}
#endregion Handle Form Submission

// Include the view
require_once __ROOT__ . '/views/admin/ihistoria.view.php';
