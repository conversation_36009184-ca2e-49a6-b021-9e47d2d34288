<?php

declare(strict_types=1);

use App\classes\Tarea;
use App\classes\Proyecto;
use App\classes\ProyectoModulo;
use App\classes\Agente;
use App\classes\TareaAgente;
use App\classes\Sprint;

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion; // Assuming $conexion is globally available or included

// Include necessary files
require_once dirname(__DIR__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en itarea.php");
	$_SESSION['flash_message_error'] = 'Error crítico: No se pudo conectar a la base de datos.';
	header('Location: ltareas');
	exit;
}

#region region INIT VARIABLES
// Variables to hold form input (useful if re-displaying form after error)
$descripcion        = '';
$id_tarea_estado    = Tarea::ESTADO_PENDIENTE; // Default to pending
$id_proyecto        = null;
$id_proyecto_modulo = null;
$id_tarea_padre     = null;
$tarea              = null;
$is_edit_mode       = false;
$error_text         = '';
$error_display      = 'none';

// Get projects for dropdown
try {
	$proyectos = Proyecto::get_list($conexion);
} catch (Exception $e) {
	$proyectos = [];
	error_log("Error loading projects: " . $e->getMessage());
}

// Initialize modules array (will be populated based on selected project)
$modulos = [];

// Get agentes for dropdown
try {
	$agentes = Agente::get_list($conexion, true); // Only active agents
} catch (Exception $e) {
	$agentes = [];
	error_log("Error loading agents: " . $e->getMessage());
}

// Get active sprint for Sprint association checkbox
$active_sprint = null;
try {
	$active_sprint = Sprint::getActiveSprint($conexion);
} catch (Exception $e) {
	error_log("Error loading active sprint: " . $e->getMessage());
	$active_sprint = null;
}

// Initialize tarea_agentes array
$tarea_agentes = [];

// Initialize tareas_padre array (will be populated later based on edit mode)
$tareas_padre = [];
#endregion INIT VARIABLES

#region region Check if Edit Mode
$tarea_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
if ($tarea_id) {
	try {
		$tarea = Tarea::get($tarea_id, $conexion);
		if ($tarea) {
			$is_edit_mode       = true;
			$descripcion        = $tarea->getDescripcion() ?? '';
			$id_tarea_estado    = $tarea->getIdTareaEstado() ?? Tarea::ESTADO_PENDIENTE;
			$id_proyecto        = $tarea->getIdProyecto();
			$id_proyecto_modulo = $tarea->getIdProyectoModulo();
			$id_tarea_padre     = $tarea->getIdTareaPadre();

			// If project is selected, load its modules
			if ($id_proyecto) {
				try {
					$modulos = ProyectoModulo::getByProyecto($id_proyecto, $conexion);
				} catch (Exception $e) {
					$modulos = [];
					error_log("Error loading modules for project ID $id_proyecto: " . $e->getMessage());
				}
			}

			// Load TareaAgente associations for this task with agent descriptions (optimized)
			try {
				$tarea_agentes = TareaAgente::getByTareaIdWithAgentDescriptions($tarea_id, $conexion);
			} catch (Exception $e) {
				$tarea_agentes = [];
				error_log("Error loading TareaAgente with agent descriptions for task ID $tarea_id: " . $e->getMessage());
			}

			// Load potential parent tasks (exclude current task to avoid circular references)
			try {
				$tareas_padre = Tarea::getPotentialParentTasks($tarea_id, $conexion);
			} catch (Exception $e) {
				$tareas_padre = [];
				error_log("Error loading potential parent tasks for task ID $tarea_id: " . $e->getMessage());
			}
		} else {
			$_SESSION['flash_message_error'] = "Error: No se encontró la tarea con ID $tarea_id.";
			header('Location: ltareas');
			exit;
		}
	} catch (Exception $e) {
		$_SESSION['flash_message_error'] = "Error al cargar la tarea: " . $e->getMessage();
		header('Location: ltareas');
		exit;
	}
} else {
	// Check for proyecto_id parameter for pre-populating project (from project navigation)
	$proyecto_id = filter_input(INPUT_GET, 'proyecto_id', FILTER_VALIDATE_INT);
	if ($proyecto_id) {
		try {
			// Validate that the project exists
			$proyecto = Proyecto::get($proyecto_id, $conexion);
			if ($proyecto) {
				$id_proyecto = $proyecto_id;

				// Load modules for the selected project
				try {
					$modulos = ProyectoModulo::getByProyecto($id_proyecto, $conexion);
				} catch (Exception $e) {
					$modulos = [];
					error_log("Error loading modules for project ID $id_proyecto: " . $e->getMessage());
				}
			} else {
				$_SESSION['flash_message_error'] = "Error: No se encontró el proyecto con ID $proyecto_id.";
				header('Location: ltareas');
				exit;
			}
		} catch (Exception $e) {
			$_SESSION['flash_message_error'] = "Error al cargar el proyecto: " . $e->getMessage();
			header('Location: ltareas');
			exit;
		}
	}

	// Check for parent_id parameter for creating child tasks
	$parent_id = filter_input(INPUT_GET, 'parent_id', FILTER_VALIDATE_INT);
	$parent_task = null; // Store parent task for display purposes
	if ($parent_id) {
		try {
			// Validate that the parent task exists
			$parent_task = Tarea::get($parent_id, $conexion);
			if ($parent_task) {
				$id_tarea_padre = $parent_id;

				// Pre-populate project and module from parent task for consistency
				$parent_proyecto_id = $parent_task->getIdProyecto();
				$parent_modulo_id = $parent_task->getIdProyectoModulo();

				if (!$id_proyecto) {
					// No project set yet - inherit both project and module from parent
					$id_proyecto = $parent_proyecto_id;
					$id_proyecto_modulo = $parent_modulo_id;
				} elseif ($id_proyecto == $parent_proyecto_id) {
					// Same project as parent - inherit the module
					$id_proyecto_modulo = $parent_modulo_id;
				}
				// If different project is already set, don't inherit module (keep existing logic)

				// If project is selected, load its modules
				if ($id_proyecto) {
					try {
						$modulos = ProyectoModulo::getByProyecto($id_proyecto, $conexion);
					} catch (Exception $e) {
						$modulos = [];
						error_log("Error loading modules for project ID $id_proyecto: " . $e->getMessage());
					}
				}
			} else {
				$_SESSION['flash_message_error'] = "Error: No se encontró la tarea padre con ID $parent_id.";
				header('Location: ltareas');
				exit;
			}
		} catch (Exception $e) {
			$_SESSION['flash_message_error'] = "Error al cargar la tarea padre: " . $e->getMessage();
			header('Location: ltareas');
			exit;
		}
	}

	// For new task creation, load all potential parent tasks
	try {
		$tareas_padre = Tarea::getPotentialParentTasks(null, $conexion);
	} catch (Exception $e) {
		$tareas_padre = [];
		error_log("Error loading potential parent tasks for new task: " . $e->getMessage());
	}
}
#endregion Check if Edit Mode

#region region POST Request Handling
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	try {
		// 1. Get data from $_POST
		$descripcion        = trim($_POST['descripcion'] ?? '');
		$id_tarea_estado    = filter_input(INPUT_POST, 'id_tarea_estado', FILTER_VALIDATE_INT) ?? Tarea::ESTADO_PENDIENTE;

		// Handle project field - convert false to null when empty
		$id_proyecto        = filter_input(INPUT_POST, 'id_proyecto', FILTER_VALIDATE_INT);
		if ($id_proyecto === false) {
			$id_proyecto = null;
		}

		// Handle module field - convert false to null when empty
		$id_proyecto_modulo = filter_input(INPUT_POST, 'id_proyecto_modulo', FILTER_VALIDATE_INT);
		if ($id_proyecto_modulo === false) {
			$id_proyecto_modulo = null;
		}

		// Handle parent task field - convert false to null when empty
		$id_tarea_padre = filter_input(INPUT_POST, 'id_tarea_padre', FILTER_VALIDATE_INT);
		if ($id_tarea_padre === false) {
			$id_tarea_padre = null;
		}

		// Handle Historia field - convert false to null when empty
		$id_historia = filter_input(INPUT_POST, 'id_historia', FILTER_VALIDATE_INT);
		if ($id_historia === false) {
			$id_historia = null;
		}

		// Handle Sprint association checkbox
		$sprint_association = isset($_POST['sprint_association']) && $_POST['sprint_association'] === 'on';
		$id_sprint = null;
		if ($sprint_association && $active_sprint) {
			$id_sprint = $active_sprint->getId();
		}

		// If project is selected, load its modules for the form
		if ($id_proyecto) {
			try {
				$modulos = ProyectoModulo::getByProyecto($id_proyecto, $conexion);

			} catch (Exception $e) {
				$modulos = [];
				error_log("Error loading modules for project ID $id_proyecto: " . $e->getMessage());
			}
		}

		// Reload potential parent tasks for form redisplay in case of errors
		try {
			$tareas_padre = Tarea::getPotentialParentTasks($is_edit_mode && $tarea ? $tarea->getId() : null, $conexion);
		} catch (Exception $e) {
			$tareas_padre = [];
			error_log("Error reloading potential parent tasks: " . $e->getMessage());
		}

		// 2. Validate data
		if (empty($descripcion)) {
			throw new Exception("La descripción de la tarea es requerida.");
		}

		if (empty($id_proyecto)) {
			throw new Exception("El proyecto es requerido.");
		}

		if (empty($id_historia)) {
			throw new Exception("La historia es requerida.");
		}

		// Validate parent task to prevent circular references
		if ($id_tarea_padre !== null && $is_edit_mode && $tarea) {
			if (!Tarea::isValidParentChild($id_tarea_padre, $tarea->getId(), $conexion)) {
				throw new Exception("La tarea seleccionada como padre no es válida. No se pueden crear referencias circulares.");
			}
		}

		// Validate Sprint inheritance for child tasks
		if ($id_tarea_padre !== null && $active_sprint) {
			try {
				$parent_task = Tarea::get($id_tarea_padre, $conexion);
				if ($parent_task) {
					$parent_has_sprint = ($parent_task->getIdSprint() === $active_sprint->getId());
					$child_has_sprint = ($id_sprint === $active_sprint->getId());

					if ($parent_has_sprint && !$child_has_sprint) {
						throw new Exception("Las tareas hijas deben heredar la asociación al sprint de su tarea padre. La tarea padre está asociada al sprint actual, por lo que esta tarea hija también debe estarlo.");
					}

					if (!$parent_has_sprint && $child_has_sprint) {
						throw new Exception("Las tareas hijas deben heredar la asociación al sprint de su tarea padre. La tarea padre no está asociada a ningún sprint, por lo que esta tarea hija tampoco puede estarlo.");
					}
				}
			} catch (Exception $e) {
				// If it's our validation error, re-throw it
				if (strpos($e->getMessage(), 'heredar la asociación al sprint') !== false) {
					throw $e;
				}
				// Otherwise, log the error but don't fail the validation
				error_log("Error validating parent task sprint association: " . $e->getMessage());
			}
		}

		// 3. Create or update Tarea
		if ($is_edit_mode && $tarea) {
			// Update existing tarea
			$tarea->setDescripcion($descripcion);
			$tarea->setIdTareaEstado($id_tarea_estado);
			$tarea->setIdProyecto($id_proyecto);
			$tarea->setIdProyectoModulo($id_proyecto_modulo);
			$tarea->setIdTareaPadre($id_tarea_padre);
			$tarea->setIdSprint($id_sprint);
			$tarea->setIdHistoria($id_historia);

			$success = $tarea->modificar($conexion);

			if ($success) {
				$_SESSION['flash_message_success'] = "Tarea actualizada exitosamente.";

				// If we came from a project context, redirect back with project filter
				$proyecto_id_param = filter_input(INPUT_GET, 'proyecto_id', FILTER_VALIDATE_INT);
				if ($proyecto_id_param) {
					$_SESSION['filtro_proyecto_id'] = $proyecto_id_param;
				}

				header('Location: ltareas');
				exit;
			} else {
				throw new Exception("Hubo un error al actualizar la tarea. Intente nuevamente.");
			}
		} else {
			// Create new tarea
			$tarea = new Tarea();
			$tarea->setDescripcion($descripcion);
			$tarea->setIdTareaEstado($id_tarea_estado);
			$tarea->setIdProyecto($id_proyecto);
			$tarea->setIdProyectoModulo($id_proyecto_modulo);
			$tarea->setIdTareaPadre($id_tarea_padre);
			$tarea->setIdSprint($id_sprint);
			$tarea->setIdHistoria($id_historia);

			$newId = $tarea->crear($conexion);

			if ($newId !== false && $newId > 0) {
				$_SESSION['flash_message_success'] = "Tarea creada exitosamente.";

				// If we came from a project context, redirect back with project filter
				$proyecto_id_param = filter_input(INPUT_GET, 'proyecto_id', FILTER_VALIDATE_INT);
				if ($proyecto_id_param) {
					$_SESSION['filtro_proyecto_id'] = $proyecto_id_param;
				}

				header('Location: ltareas');
				exit;
			} else {
				throw new Exception("Hubo un error al guardar la tarea. Intente nuevamente.");
			}
		}

	} catch (PDOException $e) {
		// Handle potential database errors
		error_log("Database error: " . $e->getMessage());
		$error_text    = 'Error de base de datos al ' . ($is_edit_mode ? 'actualizar' : 'crear') . ' la tarea. Por favor, contacte al administrador.';
		$error_display = 'show';
	} catch (Exception $e) {
		// Handle other potential errors
		error_log("General error: " . $e->getMessage());
		$error_text    = 'Ocurrió un error inesperado: ' . $e->getMessage();
		$error_display = 'show';
	}
}
#endregion POST Request Handling

// Load the view file. Variables defined above will be available in the view.
require_once __ROOT__ . '/views/admin/itarea.view.php';
