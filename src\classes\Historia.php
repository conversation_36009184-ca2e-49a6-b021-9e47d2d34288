<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Historia
{
	// --- Atributos ---
	private ?int    $id            = null;
	private ?string $titulo        = null;
	private ?string $descripcion   = null;
	private ?float  $porc_progreso = null;
	private ?int    $id_proyecto   = null;
	private ?int    $estado        = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto Historia.
	 */
	public function __construct()
	{
		$this->id            = null;
		$this->titulo        = null;
		$this->descripcion   = null;
		$this->porc_progreso = 0.0; // Progreso por defecto
		$this->id_proyecto   = null;
		$this->estado        = 1;   // Estado activo por defecto
	}

	/**
	 * Método estático para construir un objeto Historia desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos de la historia.
	 *
	 * @return self Instancia de Historia.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                = new self();
			$objeto->id            = isset($resultado['id']) ? (int)$resultado['id'] : null;
			$objeto->titulo        = $resultado['titulo'] ?? null;
			$objeto->descripcion   = $resultado['descripcion'] ?? null;
			$objeto->porc_progreso = isset($resultado['porc_progreso']) ? (float)$resultado['porc_progreso'] : 0.0;
			$objeto->id_proyecto   = isset($resultado['id_proyecto']) ? (int)$resultado['id_proyecto'] : null;
			$objeto->estado        = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			return $objeto;
		} catch (Exception $e) {
			throw new Exception("Error al construir Historia: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene una historia por su ID.
	 *
	 * @param int $id       ID de la historia.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Historia o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_by_id(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener historia por ID
			$query = <<<SQL
            SELECT
            	*
            FROM historias
            WHERE
            	id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Historia (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de historias activas.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Historia.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de historias activas
			$query = <<<SQL
            SELECT
            	*
            FROM historias
            WHERE
            	estado = 1
            ORDER BY
            	titulo
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Historias: " . $e->getMessage());
		}
	}

	/**
	 * Crea una nueva historia en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID de la nueva historia creada o false en caso de error.
	 * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
	 */
	public function crear(PDO $conexion): int|false
	{
		// Validaciones básicas
		if (empty($this->getTitulo())) {
			throw new Exception("El título es requerido para crear una historia.");
		}

		try {
			// Preparar la consulta INSERT
			$query = <<<SQL
            INSERT INTO historias (
            	 titulo
            	,descripcion
            	,porc_progreso
            	,id_proyecto
            	,estado
            ) VALUES (
            	 :titulo_crear
            	,:descripcion_crear
            	,:porc_progreso_crear
            	,:id_proyecto_crear
            	,:estado_crear
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros
			$statement->bindValue(':titulo_crear', $this->getTitulo(), PDO::PARAM_STR);
			$statement->bindValue(':descripcion_crear', $this->getDescripcion(), PDO::PARAM_STR);
			$statement->bindValue(':porc_progreso_crear', $this->getPorcProgreso(), PDO::PARAM_STR);
			$statement->bindValue(':id_proyecto_crear', $this->getIdProyecto(), ($this->getIdProyecto() === null) ? PDO::PARAM_NULL : PDO::PARAM_INT);
			$statement->bindValue(':estado_crear', $this->getEstado(), PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID de la historia recién creada
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al crear historia: " . $e->getMessage());
		}
	}

	/**
	 * Actualiza una historia existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
	 */
	public function actualizar(PDO $conexion): bool
	{
		// Validaciones básicas
		if ($this->getId() === null) {
			throw new Exception("El ID de la historia es requerido para actualizarla.");
		}

		if (empty($this->getTitulo())) {
			throw new Exception("El título es requerido para actualizar una historia.");
		}

		try {
			// Preparar la consulta UPDATE
			$query = <<<SQL
            UPDATE historias SET
            	 titulo = :titulo_actualizar
            	,descripcion = :descripcion_actualizar
            	,porc_progreso = :porc_progreso_actualizar
            	,estado = :estado_actualizar
            WHERE
            	id = :id_actualizar
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros
			$statement->bindValue(':titulo_actualizar', $this->getTitulo(), PDO::PARAM_STR);
			$statement->bindValue(':descripcion_actualizar', $this->getDescripcion(), PDO::PARAM_STR);
			$statement->bindValue(':porc_progreso_actualizar', $this->getPorcProgreso(), PDO::PARAM_STR);
			$statement->bindValue(':estado_actualizar', $this->getEstado(), PDO::PARAM_INT);
			$statement->bindValue(':id_actualizar', $this->getId(), PDO::PARAM_INT);

			// Ejecutar la consulta
			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al actualizar historia (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Desactiva una historia estableciendo su estado a 0.
	 *
	 * @param int $id       ID de la historia a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 0 (inactivo)
			$query = <<<SQL
            UPDATE historias SET
            	estado = 0
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al desactivar historia (ID: $id): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getTitulo(): ?string
	{
		return $this->titulo;
	}

	public function setTitulo(?string $titulo): self
	{
		$this->titulo = $titulo;
		return $this;
	}

	public function getDescripcion(): ?string
	{
		return $this->descripcion;
	}

	public function setDescripcion(?string $descripcion): self
	{
		$this->descripcion = $descripcion;
		return $this;
	}

	public function getPorcProgreso(): ?float
	{
		return $this->porc_progreso;
	}

	public function setPorcProgreso(?float $porc_progreso): self
	{
		$this->porc_progreso = $porc_progreso;
		return $this;
	}

	public function getIdProyecto(): ?int
	{
		return $this->id_proyecto;
	}

	public function setIdProyecto(?int $id_proyecto): self
	{
		$this->id_proyecto = $id_proyecto;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	// --- Métodos de relación ---

	/**
	 * Obtiene historias por ID de proyecto.
	 *
	 * @param int $id_proyecto ID del proyecto.
	 * @param PDO $conexion    Conexión PDO.
	 *
	 * @return array Array de objetos Historia.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_by_proyecto_id(int $id_proyecto, PDO $conexion): array
	{
		try {
			$query = <<<SQL
            SELECT
            	*
            FROM historias
            WHERE
            	id_proyecto = :id_proyecto_filtro
            	AND estado = 1
            ORDER BY
            	titulo
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_proyecto_filtro", $id_proyecto, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Historias por Proyecto (ID: $id_proyecto): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene el objeto Proyecto asociado a esta historia.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return \App\classes\Proyecto|null Objeto Proyecto o null si no está asociado a ningún proyecto.
	 * @throws Exception Si hay error en DB.
	 */
	public function getProyecto(PDO $conexion): ?\App\classes\Proyecto
	{
		if ($this->id_proyecto === null) {
			return null;
		}

		return \App\classes\Proyecto::get($this->id_proyecto, $conexion);
	}

	// --- Métodos adicionales ---

	/**
	 * Verifica si la historia está activa.
	 * @return bool
	 */
	public function isActiva(): bool
	{
		return $this->estado === 1;
	}
}
